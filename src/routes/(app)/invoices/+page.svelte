<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { goto } from '$app/navigation';
  import {
    getInvoices,
    getInvoiceStatuses,
    getInvoiceTemplates,
    createInvoiceTemplate,
    updateInvoiceTemplate,
    deleteInvoiceTemplate,
    type InvoiceTemplate,
    type TemplateSection
  } from '$lib/api/invoices';
  import type { Invoice, InvoiceStatus } from '$lib/api/invoices';
  import { formatCurrency } from '$lib/config/currency';

  // Tab state
  let activeTab: 'invoices' | 'designer' = 'invoices';

  // Invoice list variables
  let invoices: Invoice[] = [];
  let invoiceStatuses: InvoiceStatus[] = [];
  let loading = true;
  let searchQuery = '';
  let statusFilter = 'All';

  // Template designer variables
  let isTemplateLoading = true;
  let isSaving = false;
  let templates: InvoiceTemplate[] = [];
  let selectedTemplate: InvoiceTemplate | null = null;
  let isEditing = false;
  let showPreview = false;

  // Template form data
  let templateForm: {
    name: string;
    isDefault: boolean;
    logoUrl: string;
    colorScheme: {
      primary: string;
      secondary: string;
      accent: string;
      background?: string;
      text?: string;
    };
    sections: TemplateSection[];
  } = {
    name: '',
    isDefault: false,
    logoUrl: '',
    colorScheme: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#10b981',
      background: '#ffffff',
      text: '#000000'
    },
    sections: [
      {
        id: 'header',
        type: 'header',
        content: 'INVOICE',
        order: 1,
        isVisible: true
      },
      {
        id: 'terms',
        type: 'terms',
        content: 'Payment due within 30 days. Please make payment via bank transfer.',
        order: 2,
        isVisible: true
      },
      {
        id: 'footer',
        type: 'footer',
        content: 'Thank you for your business!',
        order: 3,
        isVisible: true
      }
    ]
  };

  // File input for logo upload
  let logoFileInput: HTMLInputElement;

  onMount(async () => {
    await loadData();
    if (activeTab === 'designer') {
      await loadTemplates();
    }
  });

  // Watch for tab changes to load templates when needed
  $: if (activeTab === 'designer' && templates.length === 0) {
    loadTemplates();
  }

  async function loadData() {
    loading = true;
    try {
      const [invoicesData, statusesData] = await Promise.all([
        getInvoices(),
        getInvoiceStatuses()
      ]);
      invoices = invoicesData;
      invoiceStatuses = statusesData;
    } catch (error) {
      console.error('Error loading invoices:', error);
      addToast({ message: 'Failed to load invoices', type: 'error' });
    } finally {
      loading = false;
    }
  }

  async function loadTemplates() {
    isTemplateLoading = true;
    try {
      templates = await getInvoiceTemplates();
    } catch (error) {
      console.error('Error loading templates:', error);
      addToast({ message: 'Failed to load templates', type: 'error' });
    } finally {
      isTemplateLoading = false;
    }
  }

  // Filtered invoices based on search and status
  $: filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         invoice.customerName?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'All' || invoice.status.name === statusFilter;

    return matchesSearch && matchesStatus;
  });

  function handleCreateInvoice() {
    goto('/invoices/new');
  }

  function handleViewInvoice(invoice: Invoice) {
    goto(`/invoices/${invoice.id}`);
  }

  function getStatusColor(status: InvoiceStatus): string {
    return status.color;
  }



  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  function getOverdueInvoices(): Invoice[] {
    const today = new Date();
    return invoices.filter(invoice =>
      invoice.status.name !== 'Paid' &&
      invoice.status.name !== 'Cancelled' &&
      new Date(invoice.dueDate) < today
    );
  }

  function getDraftInvoices(): Invoice[] {
    return invoices.filter(invoice => invoice.status.name === 'Draft');
  }

  function getPaidInvoices(): Invoice[] {
    return invoices.filter(invoice => invoice.status.name === 'Paid');
  }

  function getTotalRevenue(): number {
    return invoices
      .filter(invoice => invoice.status.name === 'Paid')
      .reduce((total, invoice) => total + invoice.totalAmount, 0);
  }

  // Template designer functions
  function startNewTemplate() {
    selectedTemplate = null;
    isEditing = true;
    resetForm();
  }

  function editTemplate(template: InvoiceTemplate) {
    selectedTemplate = template;
    isEditing = true;
    populateForm(template);
  }

  function resetForm() {
    templateForm = {
      name: '',
      isDefault: false,
      logoUrl: '',
      colorScheme: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#10b981',
        background: '#ffffff',
        text: '#000000'
      },
      sections: [
        {
          id: 'header',
          type: 'header' as const,
          content: 'INVOICE',
          order: 1,
          isVisible: true
        },
        {
          id: 'terms',
          type: 'terms' as const,
          content: 'Payment due within 30 days. Please make payment via bank transfer.',
          order: 2,
          isVisible: true
        },
        {
          id: 'footer',
          type: 'footer' as const,
          content: 'Thank you for your business!',
          order: 3,
          isVisible: true
        }
      ]
    };
  }

  function populateForm(template: InvoiceTemplate) {
    templateForm = {
      name: template.name,
      isDefault: template.isDefault,
      logoUrl: template.logoUrl || '',
      colorScheme: template.colorScheme || {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#10b981'
      },
      sections: template.sections || []
    };
  }

  function handleLogoUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (file) {
      // In a real app, you would upload to a server
      // For now, we'll create a data URL
      const reader = new FileReader();
      reader.onload = (e) => {
        templateForm.logoUrl = e.target?.result as string;
      };
      reader.readAsDataURL(file);

      addToast({ message: 'Logo uploaded successfully', type: 'success' });
    }
  }

  function addTextSection() {
    const newSection = {
      id: `section_${Date.now()}`,
      type: 'custom' as const,
      content: 'Enter your text here...',
      order: templateForm.sections.length + 1,
      isVisible: true
    };

    templateForm.sections = [...templateForm.sections, newSection];
  }

  function removeTextSection(index: number) {
    templateForm.sections = templateForm.sections.filter((_, i) => i !== index);
  }

  async function saveTemplate() {
    if (!templateForm.name.trim()) {
      addToast({ message: 'Template name is required', type: 'error' });
      return;
    }

    isSaving = true;
    try {
      const templateData = {
        name: templateForm.name,
        isDefault: templateForm.isDefault,
        logoUrl: templateForm.logoUrl,
        colorScheme: templateForm.colorScheme,
        sections: templateForm.sections
      };

      if (selectedTemplate) {
        await updateInvoiceTemplate(selectedTemplate.id, templateData);
        addToast({ message: 'Template updated successfully', type: 'success' });
      } else {
        await createInvoiceTemplate(templateData);
        addToast({ message: 'Template created successfully', type: 'success' });
      }

      await loadTemplates();
      cancelEdit();
    } catch (error) {
      console.error('Error saving template:', error);
      addToast({ message: 'Failed to save template', type: 'error' });
    } finally {
      isSaving = false;
    }
  }

  async function deleteTemplate(template: InvoiceTemplate) {
    if (!confirm(`Are you sure you want to delete the template "${template.name}"?`)) {
      return;
    }

    try {
      await deleteInvoiceTemplate(template.id);
      addToast({ message: 'Template deleted successfully', type: 'success' });
      await loadTemplates();

      if (selectedTemplate?.id === template.id) {
        cancelEdit();
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      addToast({ message: 'Failed to delete template', type: 'error' });
    }
  }

  function cancelEdit() {
    isEditing = false;
    selectedTemplate = null;
    showPreview = false;
    resetForm();
  }

  function togglePreview() {
    showPreview = !showPreview;
  }
</script>

<svelte:head>
  <title>Invoices</title>
</svelte:head>

<div class="container">
  <PageHeader title="Invoices">
    <svelte:fragment slot="actions">
      <Button on:click={handleCreateInvoice} variant="primary" type="button">
        Create Invoice
      </Button>
    </svelte:fragment>
  </PageHeader>

  <!-- Tab Navigation -->
  <div class="tab-navigation">
    <button
      class="tab-button"
      class:active={activeTab === 'invoices'}
      on:click={() => activeTab = 'invoices'}
    >
      Invoices
    </button>
    <button
      class="tab-button"
      class:active={activeTab === 'designer'}
      on:click={() => activeTab = 'designer'}
    >
      Template Designer
    </button>
  </div>

  <main>
    {#if activeTab === 'invoices'}
      {#if loading}
        <div class="loading-container">
          <LoadingSpinner />
          <p>Loading invoices...</p>
        </div>
      {:else}
      <!-- Stats -->
      <div class="stats">
        <div class="stat-card">
          <h3>Total Revenue</h3>
          <p class="stat-number">{formatCurrency(getTotalRevenue())}</p>
        </div>
        <div class="stat-card">
          <h3>Total Invoices</h3>
          <p class="stat-number">{invoices.length}</p>
        </div>
        <div class="stat-card">
          <h3>Paid</h3>
          <p class="stat-number">{getPaidInvoices().length}</p>
        </div>
        <div class="stat-card">
          <h3>Overdue</h3>
          <p class="stat-number overdue">{getOverdueInvoices().length}</p>
        </div>
        <div class="stat-card">
          <h3>Drafts</h3>
          <p class="stat-number">{getDraftInvoices().length}</p>
        </div>
      </div>

      <!-- Filters and Search -->
      <div class="controls">
        <div class="search-section">
          <input
            type="text"
            placeholder="Search invoices..."
            bind:value={searchQuery}
            class="search-input"
          />
        </div>

        <div class="filter-section">
          <label for="status-filter">Status:</label>
          <select id="status-filter" bind:value={statusFilter}>
            <option value="All">All</option>
            {#each invoiceStatuses as status}
              <option value={status.name}>{status.name}</option>
            {/each}
          </select>
        </div>
      </div>

      <!-- Invoices List -->
      {#if filteredInvoices.length === 0}
        <div class="empty-state">
          <h3>No invoices found</h3>
          <p>
            {#if searchQuery || statusFilter !== 'All'}
              Try adjusting your search or filters.
            {:else}
              Get started by creating your first invoice.
            {/if}
          </p>
          {#if !searchQuery && statusFilter === 'All'}
            <Button on:click={handleCreateInvoice} variant="primary">
              Create Invoice
            </Button>
          {/if}
        </div>
      {:else}
        <div class="invoices-table">
          <div class="table-header">
            <div class="header-cell">Invoice #</div>
            <div class="header-cell">Customer</div>
            <div class="header-cell">Issue Date</div>
            <div class="header-cell">Due Date</div>
            <div class="header-cell">Amount</div>
            <div class="header-cell">Status</div>
            <div class="header-cell">Actions</div>
          </div>

          {#each filteredInvoices as invoice (invoice.id)}
            <div class="table-row" on:click={() => handleViewInvoice(invoice)} role="button" tabindex="0">
              <div class="table-cell">
                <span class="invoice-number">{invoice.invoiceNumber}</span>
              </div>
              <div class="table-cell">
                <span class="customer-name">{invoice.customerName || 'Unknown Customer'}</span>
              </div>
              <div class="table-cell">
                <span class="date">{formatDate(invoice.issueDate)}</span>
              </div>
              <div class="table-cell">
                <span class="date" class:overdue={new Date(invoice.dueDate) < new Date() && invoice.status.name !== 'Paid'}>
                  {formatDate(invoice.dueDate)}
                </span>
              </div>
              <div class="table-cell">
                <span class="amount">{formatCurrency(invoice.totalAmount)}</span>
              </div>
              <div class="table-cell">
                <span class="status-badge" style="background-color: {getStatusColor(invoice.status)}">
                  {invoice.status.name}
                </span>
              </div>
              <div class="table-cell">
                <Button
                  variant="secondary"
                  size="small"
                  on:click={(e) => { e.stopPropagation(); handleViewInvoice(invoice); }}
                >
                  View
                </Button>
              </div>
            </div>
          {/each}
        </div>
      {/if}
      {/if}
    {:else if activeTab === 'designer'}
      <!-- Template Designer Content -->
      {#if isTemplateLoading}
        <div class="loading-container">
          <LoadingSpinner />
          <p>Loading templates...</p>
        </div>
      {:else}
        <div class="designer-container">
          {#if !isEditing}
            <!-- Template List View -->
            <div class="templates-section">
              <div class="section-header">
                <h2>Invoice Templates</h2>
                <Button variant="primary" on:click={startNewTemplate}>
                  Create New Template
                </Button>
              </div>

              {#if templates.length === 0}
                <div class="empty-state">
                  <p>No templates found. Create your first invoice template to get started.</p>
                  <Button variant="primary" on:click={startNewTemplate}>
                    Create Template
                  </Button>
                </div>
              {:else}
                <div class="templates-grid">
                  {#each templates as template}
                    <div class="template-card">
                      <div class="template-preview">
                        {#if template.logoUrl}
                          <img src={template.logoUrl} alt="Logo" class="template-logo" />
                        {/if}
                        <div class="template-header" style="color: {template.colorScheme?.primary || '#2563eb'}">
                          INVOICE
                        </div>
                        <div class="template-content">
                          <div class="sample-line"></div>
                          <div class="sample-line short"></div>
                          <div class="sample-line"></div>
                        </div>
                      </div>

                      <div class="template-info">
                        <h3>{template.name}</h3>
                        {#if template.isDefault}
                          <span class="default-badge">Default</span>
                        {/if}
                      </div>

                      <div class="template-actions">
                        <Button variant="secondary" size="small" on:click={() => editTemplate(template)}>
                          Edit
                        </Button>
                        <Button variant="tertiary" size="small" on:click={() => deleteTemplate(template)}>
                          Delete
                        </Button>
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {:else}
            <!-- Template Editor View -->
            <div class="editor-container">
              <div class="editor-header">
                <h2>{selectedTemplate ? 'Edit Template' : 'Create New Template'}</h2>
                <div class="editor-actions">
                  <Button variant="secondary" on:click={togglePreview}>
                    {showPreview ? 'Hide Preview' : 'Show Preview'}
                  </Button>
                  <Button variant="tertiary" on:click={cancelEdit}>Cancel</Button>
                  <Button variant="primary" on:click={saveTemplate} disabled={isSaving}>
                    {isSaving ? 'Saving...' : 'Save Template'}
                  </Button>
                </div>
              </div>

              <div class="editor-content" class:with-preview={showPreview}>
                <!-- Editor Panel -->
                <div class="editor-panel">
                  <!-- Basic Settings -->
                  <div class="editor-section">
                    <h3>Basic Settings</h3>

                    <div class="form-group">
                      <label for="templateName">Template Name</label>
                      <input
                        type="text"
                        id="templateName"
                        bind:value={templateForm.name}
                        placeholder="Enter template name"
                      />
                    </div>

                    <div class="form-group">
                      <label class="checkbox-label">
                        <input
                          type="checkbox"
                          bind:checked={templateForm.isDefault}
                        />
                        Set as default template
                      </label>
                    </div>
                  </div>

                  <!-- Logo Upload -->
                  <div class="editor-section">
                    <h3>Logo</h3>

                    <div class="logo-upload">
                      {#if templateForm.logoUrl}
                        <div class="logo-preview">
                          <img src={templateForm.logoUrl} alt="Logo preview" />
                          <Button variant="tertiary" size="small" on:click={() => templateForm.logoUrl = ''}>
                            Remove
                          </Button>
                        </div>
                      {:else}
                        <div class="logo-placeholder">
                          <p>No logo uploaded</p>
                        </div>
                      {/if}

                      <input
                        type="file"
                        accept="image/*"
                        bind:this={logoFileInput}
                        on:change={handleLogoUpload}
                        style="display: none;"
                      />

                      <Button variant="secondary" on:click={() => logoFileInput.click()}>
                        Upload Logo
                      </Button>
                    </div>
                  </div>

                  <!-- Color Scheme -->
                  <div class="editor-section">
                    <h3>Color Scheme</h3>

                    <div class="color-grid">
                      <div class="color-input">
                        <label for="primaryColor">Primary Color</label>
                        <input
                          type="color"
                          id="primaryColor"
                          bind:value={templateForm.colorScheme.primary}
                        />
                      </div>

                      <div class="color-input">
                        <label for="secondaryColor">Secondary Color</label>
                        <input
                          type="color"
                          id="secondaryColor"
                          bind:value={templateForm.colorScheme.secondary}
                        />
                      </div>

                      <div class="color-input">
                        <label for="accentColor">Accent Color</label>
                        <input
                          type="color"
                          id="accentColor"
                          bind:value={templateForm.colorScheme.accent}
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Text Sections -->
                  <div class="editor-section">
                    <div class="section-header">
                      <h3>Text Sections</h3>
                      <Button variant="secondary" size="small" on:click={addTextSection}>
                        Add Section
                      </Button>
                    </div>

                    {#each templateForm.sections as section, index}
                      <div class="text-section">
                        <div class="section-controls">
                          <input
                            type="text"
                            bind:value={section.content}
                            placeholder="Section content"
                            class="section-name"
                          />

                          <label class="checkbox-label">
                            <input
                              type="checkbox"
                              bind:checked={section.isVisible}
                            />
                            Visible
                          </label>

                          <Button variant="tertiary" size="small" on:click={() => removeTextSection(index)}>
                            Remove
                          </Button>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>

                <!-- Preview Panel -->
                {#if showPreview}
                  <div class="preview-panel">
                    <h3>Template Preview</h3>
                    <div class="preview-content" style="background: {templateForm.colorScheme.background}; color: {templateForm.colorScheme.text}">
                      {#if templateForm.logoUrl}
                        <img src={templateForm.logoUrl} alt="Logo" class="preview-logo" />
                      {/if}

                      <div class="preview-header" style="color: {templateForm.colorScheme.primary}">
                        INVOICE
                      </div>

                      {#each templateForm.sections.filter(s => s.isVisible) as section}
                        <div class="preview-section">
                          {section.content}
                        </div>
                      {/each}
                    </div>
                  </div>
                {/if}
              </div>
            </div>
          {/if}
        </div>
      {/if}
    {/if}
  </main>
</div>

<style lang="less">
  .tab-navigation {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 2rem;

    .tab-button {
      background: none;
      border: none;
      padding: 1rem 2rem;
      font-size: 1rem;
      font-weight: 500;
      color: var(--grey);
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s;

      &:hover {
        color: var(--primary);
        background: var(--bg);
      }

      &.active {
        color: var(--primary);
        border-bottom-color: var(--primary);
      }
    }
  }

  main {
    padding: 1rem 2rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .stat-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;
      text-align: center;

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-number {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--primary);

        &.overdue {
          color: #EF4444;
        }
      }
    }
  }

  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    .search-section {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--black);
      }

      select {
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        min-width: 120px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .invoices-table {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;

    .table-header {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .header-cell {
        padding: 1rem;
        font-weight: 600;
        color: var(--black);
        font-size: 0.9rem;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      border-bottom: 1px solid var(--border);
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: var(--bg);
      }

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        padding: 1rem;
        display: flex;
        align-items: center;
        font-size: 0.9rem;

        .invoice-number {
          font-weight: 600;
          color: var(--primary);
        }

        .customer-name {
          color: var(--black);
        }

        .date {
          color: var(--grey);

          &.overdue {
            color: #EF4444;
            font-weight: 500;
          }
        }

        .amount {
          font-weight: 600;
          color: var(--black);
        }

        .status-badge {
          font-size: 0.7rem;
          font-weight: 500;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .controls {
      flex-direction: column;
      align-items: stretch;
    }

    .invoices-table {
      .table-header,
      .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .header-cell,
      .table-cell {
        padding: 0.5rem 1rem;
      }

      .table-row {
        padding: 1rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        margin-bottom: 1rem;
        background: white;

        .table-cell {
          display: flex;
          justify-content: space-between;
          padding: 0.25rem 0;

          &::before {
            content: attr(data-label);
            font-weight: 600;
            color: var(--grey);
          }
        }
      }
    }
  }

  // Template Designer Styles
  .designer-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;

    h2 {
      margin: 0;
      color: var(--black);
    }
  }

  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .template-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
    transition: box-shadow 0.2s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .template-preview {
      padding: 2rem;
      background: #f8f9fa;
      border-bottom: 1px solid var(--border);
      min-height: 200px;
      position: relative;

      .template-logo {
        max-width: 60px;
        max-height: 40px;
        margin-bottom: 1rem;
      }

      .template-header {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
      }

      .template-content {
        .sample-line {
          height: 8px;
          background: #e5e7eb;
          margin-bottom: 0.5rem;
          border-radius: 4px;

          &.short {
            width: 60%;
          }
        }
      }
    }

    .template-info {
      padding: 1rem;

      h3 {
        margin: 0 0 0.5rem 0;
        color: var(--black);
      }

      .default-badge {
        background: var(--primary);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: var(--br);
        font-size: 0.75rem;
        font-weight: 500;
      }
    }

    .template-actions {
      padding: 1rem;
      border-top: 1px solid var(--border);
      display: flex;
      gap: 0.5rem;
    }
  }

  .editor-container {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
  }

  .editor-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      color: var(--black);
    }

    .editor-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .editor-content {
    display: grid;
    grid-template-columns: 1fr;
    min-height: 600px;

    &.with-preview {
      grid-template-columns: 1fr 1fr;
    }
  }

  .editor-panel {
    padding: 2rem;
    border-right: 1px solid var(--border);
    overflow-y: auto;
    max-height: 80vh;
  }

  .editor-section {
    margin-bottom: 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
      font-size: 1.1rem;
    }

    .form-group {
      margin-bottom: 1rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--black);

        &.checkbox-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;

          input[type="checkbox"] {
            margin: 0;
          }
        }
      }

      input[type="text"],
      input[type="number"],
      select,
      textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }
  }

  .logo-upload {
    .logo-preview {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;

      img {
        max-width: 100px;
        max-height: 60px;
        border: 1px solid var(--border);
        border-radius: var(--br);
      }
    }

    .logo-placeholder {
      padding: 2rem;
      border: 2px dashed var(--border);
      border-radius: var(--br);
      text-align: center;
      margin-bottom: 1rem;

      p {
        margin: 0;
        color: var(--grey);
      }
    }
  }

  .color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;

    .color-input {
      label {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }

      input[type="color"] {
        width: 100%;
        height: 40px;
        border: 1px solid var(--border);
        border-radius: var(--br);
        cursor: pointer;
      }
    }
  }

  .text-section {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;
    margin-bottom: 1rem;

    .section-controls {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;

      .section-name {
        flex: 1;
      }

      .checkbox-label {
        white-space: nowrap;
      }
    }
  }

  .preview-panel {
    padding: 2rem;
    background: #f8f9fa;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    .preview-content {
      background: white;
      padding: 2rem;
      border-radius: var(--br);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .preview-logo {
        max-width: 120px;
        max-height: 80px;
        margin-bottom: 1rem;
      }

      .preview-header {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 2rem;
        text-align: center;
      }

      .preview-section {
        margin-bottom: 1rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e5e7eb;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
</style>
