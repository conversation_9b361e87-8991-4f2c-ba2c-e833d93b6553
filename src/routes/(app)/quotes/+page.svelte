<script lang="ts">
  import { onMount } from 'svelte';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { goto } from '$app/navigation';
  import { getQuotes, getQuoteStatuses } from '$lib/api/quotes';
  import type { Quote, QuoteStatus } from '$lib/api/quotes';
  import { formatCurrency } from '$lib/config/currency';

  let quotes: Quote[] = [];
  let quoteStatuses: QuoteStatus[] = [];
  let loading = true;
  let searchQuery = '';
  let statusFilter = 'All';

  onMount(async () => {
    await loadData();
  });

  async function loadData() {
    loading = true;
    try {
      const [quotesData, statusesData] = await Promise.all([
        getQuotes(),
        getQuoteStatuses()
      ]);
      quotes = quotesData;
      quoteStatuses = statusesData;
    } catch (error) {
      console.error('Error loading quotes:', error);
      addToast({ message: 'Failed to load quotes', type: 'error' });
    } finally {
      loading = false;
    }
  }

  // Filtered quotes based on search and status
  $: filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.quoteNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.customerName?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'All' || quote.status.name === statusFilter;

    return matchesSearch && matchesStatus;
  });

  function handleCreateQuote() {
    goto('/quotes/create');
  }

  function handleViewQuote(quote: Quote) {
    goto(`/quotes/${quote.id}`);
  }

  function getStatusColor(status: QuoteStatus): string {
    return status.color;
  }



  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  function getDraftQuotes(): Quote[] {
    return quotes.filter(quote => quote.status.name === 'Draft');
  }

  function getSentQuotes(): Quote[] {
    return quotes.filter(quote => quote.status.name === 'Sent');
  }

  function getAcceptedQuotes(): Quote[] {
    return quotes.filter(quote => quote.status.name === 'Accepted');
  }

  function getExpiredQuotes(): Quote[] {
    const today = new Date();
    return quotes.filter(quote =>
      quote.status.name !== 'Accepted' &&
      quote.status.name !== 'Converted' &&
      new Date(quote.expiryDate) < today
    );
  }

  function getTotalQuoteValue(): number {
    return quotes
      .filter(quote => quote.status.name === 'Accepted')
      .reduce((total, quote) => total + quote.totalAmount, 0);
  }
</script>

<svelte:head>
  <title>Quotes</title>
</svelte:head>

<div class="container">
  <PageHeader title="Quotes">
    <svelte:fragment slot="actions">
      <Button on:click={handleCreateQuote} variant="primary" type="button">
        Create Quote
      </Button>
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if loading}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading quotes...</p>
      </div>
    {:else}
      <!-- Stats -->
      <div class="stats">
        <div class="stat-card">
          <h3>Total Quote Value</h3>
          <p class="stat-number">{formatCurrency(getTotalQuoteValue())}</p>
        </div>
        <div class="stat-card">
          <h3>Total Quotes</h3>
          <p class="stat-number">{quotes.length}</p>
        </div>
        <div class="stat-card">
          <h3>Accepted</h3>
          <p class="stat-number">{getAcceptedQuotes().length}</p>
        </div>
        <div class="stat-card">
          <h3>Sent</h3>
          <p class="stat-number">{getSentQuotes().length}</p>
        </div>
        <div class="stat-card">
          <h3>Expired</h3>
          <p class="stat-number expired">{getExpiredQuotes().length}</p>
        </div>
        <div class="stat-card">
          <h3>Drafts</h3>
          <p class="stat-number">{getDraftQuotes().length}</p>
        </div>
      </div>

      <!-- Filters and Search -->
      <div class="controls">
        <div class="search-section">
          <input
            type="text"
            placeholder="Search quotes..."
            bind:value={searchQuery}
            class="search-input"
          />
        </div>

        <div class="filter-section">
          <label for="status-filter">Status:</label>
          <select id="status-filter" bind:value={statusFilter}>
            <option value="All">All</option>
            {#each quoteStatuses as status}
              <option value={status.name}>{status.name}</option>
            {/each}
          </select>
        </div>
      </div>

      <!-- Quotes List -->
      {#if filteredQuotes.length === 0}
        <div class="empty-state">
          <h3>No quotes found</h3>
          <p>
            {#if searchQuery || statusFilter !== 'All'}
              Try adjusting your search or filters.
            {:else}
              Get started by creating your first quote.
            {/if}
          </p>
          {#if !searchQuery && statusFilter === 'All'}
            <Button on:click={handleCreateQuote} variant="primary">
              Create Quote
            </Button>
          {/if}
        </div>
      {:else}
        <div class="quotes-table">
          <div class="table-header">
            <div class="header-cell">Quote #</div>
            <div class="header-cell">Customer</div>
            <div class="header-cell">Issue Date</div>
            <div class="header-cell">Expiry Date</div>
            <div class="header-cell">Amount</div>
            <div class="header-cell">Status</div>
            <div class="header-cell">Actions</div>
          </div>

          {#each filteredQuotes as quote (quote.id)}
            <div class="table-row" on:click={() => handleViewQuote(quote)} role="button" tabindex="0">
              <div class="table-cell">
                <span class="quote-number">{quote.quoteNumber}</span>
              </div>
              <div class="table-cell">
                <span class="customer-name">{quote.customerName || 'Unknown Customer'}</span>
              </div>
              <div class="table-cell">
                <span class="date">{formatDate(quote.issueDate)}</span>
              </div>
              <div class="table-cell">
                <span class="date" class:expired={new Date(quote.expiryDate) < new Date() && quote.status.name !== 'Accepted'}>
                  {formatDate(quote.expiryDate)}
                </span>
              </div>
              <div class="table-cell">
                <span class="amount">{formatCurrency(quote.totalAmount)}</span>
              </div>
              <div class="table-cell">
                <span class="status-badge" style="background-color: {getStatusColor(quote.status)}">
                  {quote.status.name}
                </span>
              </div>
              <div class="table-cell">
                <Button
                  variant="secondary"
                  size="small"
                  on:click={(e) => { e.stopPropagation(); handleViewQuote(quote); }}
                >
                  View
                </Button>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    {/if}
  </main>
</div>

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .stat-card {
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      padding: 1.5rem;
      text-align: center;

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .stat-number {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--primary);

        &.expired {
          color: #EF4444;
        }
      }
    }
  }

  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;

    .search-section {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 0.9rem;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px var(--primary-fade);
        }
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--black);
      }

      select {
        padding: 0.5rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        min-width: 120px;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }

    p {
      margin: 0 0 2rem 0;
      color: var(--grey);
    }
  }

  .quotes-table {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;

    .table-header {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .header-cell {
        padding: 1rem;
        font-weight: 600;
        color: var(--black);
        font-size: 0.9rem;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr auto;
      border-bottom: 1px solid var(--border);
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: var(--bg);
      }

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        padding: 1rem;
        display: flex;
        align-items: center;
        font-size: 0.9rem;

        .quote-number {
          font-weight: 600;
          color: var(--primary);
        }

        .customer-name {
          color: var(--black);
        }

        .date {
          color: var(--grey);

          &.expired {
            color: #EF4444;
            font-weight: 500;
          }
        }

        .amount {
          font-weight: 600;
          color: var(--black);
        }

        .status-badge {
          font-size: 0.7rem;
          font-weight: 500;
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: var(--br);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .controls {
      flex-direction: column;
      align-items: stretch;
    }

    .quotes-table {
      .table-header,
      .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .header-cell,
      .table-cell {
        padding: 0.5rem 1rem;
      }

      .table-row {
        padding: 1rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        margin-bottom: 1rem;
        background: white;

        .table-cell {
          display: flex;
          justify-content: space-between;
          padding: 0.25rem 0;

          &::before {
            content: attr(data-label);
            font-weight: 600;
            color: var(--grey);
          }
        }
      }
    }
  }
</style>
