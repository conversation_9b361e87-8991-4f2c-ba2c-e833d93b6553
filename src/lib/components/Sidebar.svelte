<script lang="ts">
  import { page } from '$app/stores';
  import { user, logout } from '$lib/stores/auth';
  import { goto } from '$app/navigation';
  import Logo from '$lib/components/icons/Logo.svelte';
  import IconDashboard from '$lib/components/icons/IconDashboard.svelte';
  import IconTeam from '$lib/components/icons/IconTeam.svelte';
  import IconQuote from '$lib/components/icons/IconQuote.svelte';
  import IconInvoice from '$lib/components/icons/IconInvoice.svelte';
    import IconJob from './icons/IconJob.svelte';

  let currentUser: { email: string } | null = null;

  // Subscribe to user store
  user.subscribe(value => {
    currentUser = value;
  });

  function handleLogout() {
    logout();
    goto('/login');
  }

  // Navigation items
  const navItems = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: IconDashboard
    },
    {
      name: 'Contacts',
      path: '/contacts',
      icon: IconTeam
    },
    {
      name: 'Jobs',
      path: '/jobs',
      icon: IconJob
    },
    {
      name: 'Quotes',
      path: '/quotes',
      icon: IconQuote
    },
    {
      name: 'Invoices',
      path: '/invoices',
      icon: IconInvoice
    },
    {
      name: 'Staff',
      path: '/staff',
      icon: IconTeam
    },
    {
      name: 'Calendar',
      path: '/calendar',
      icon: IconDashboard
    },
    {
      name: 'Button Example',
      path: '/button-example',
      icon: IconDashboard
    }
    // Add more navigation items as needed
  ];

  // Check if the current path matches the nav item path
  function isActive(path: string) {
    return $page.url.pathname.startsWith(path);
  }
</script>

<div class="sidebar">

  <div class="sidebar-container">
    <div class="logo-container">
      <Logo />
    </div>

    <nav class="nav-menu">
      <ul>
        {#each navItems as item}
          <li class:active={isActive(item.path)}>
            <a href={item.path}>
              <span class="icon">
                <svelte:component this={item.icon} />
              </span>
              <span class="text">{item.name}</span>
            </a>
          </li>
        {/each}
      </ul>
    </nav>

    <div class="user-section">
      {#if currentUser}
        <div class="user-info">
          <div class="user-email">{currentUser.email}</div>
          <button on:click={handleLogout} class="logout-button">Logout</button>
        </div>
      {/if}
    </div>
  </div>

</div>

<style lang="less">

  .sidebar {
    height: 100%;
    overflow: auto;
    border-right: 1px solid var(--border);
  }

  .sidebar-container {
    height: 100vh;
    background-color: var(--white);
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .logo-container {
    padding: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid var(--border);

    :global(.ejp-logo) {
      width: 120px;
      height: auto;
    }

    :global(.primary-fill) {
      fill: hsl(var(--primary-hs) var(--primary-l));
    }

    :global(.secondary-fill) {
      fill: hsl(var(--secondary-hs) var(--secondary-l));
    }

    :global(.dark-white) {
      fill: var(--white);
    }
  }

  .nav-menu {
    flex: 1;
    padding: 1.5rem 0;

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    li {

      &.active {
        a {
          background-color: var(--bg);
          color: hsl(var(--primary-hs) var(--primary-l));
          font-weight: 500;

          .icon :global(svg path) {
            fill: hsl(var(--primary-hs) var(--primary-l));
          }
        }
      }

      a {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        color: var(--grey);
        text-decoration: none;
        transition: background-color 0.2s, color 0.2s;

        &:hover {
          background-color: var(--bg);
        }

        .icon {
          margin-right: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
        }

        .text {
          font-size: 0.9rem;
        }
      }
    }
  }

  .user-section {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border);

    .user-info {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .user-email {
      font-size: 0.9rem;
      color: var(--grey);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .logout-button {
      width: 100%;
      padding: 0.5rem;
      background-color: transparent;
      color: var(--grey);
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 0.8rem;
      cursor: pointer;
      transition: background-color 0.2s, color 0.2s;

      &:hover {
        background-color: hsl(var(--primary-hs) var(--primary-l));
        color: var(--white);
        border-color: hsl(var(--primary-hs) var(--primary-l));
      }
    }
  }
</style>
